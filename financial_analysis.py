import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# Mac系统中文字体配置 - 使用成功的配置
import matplotlib.font_manager as fm
import platform
import os

print(f"系统: {platform.system()}")

# 清除matplotlib字体缓存
try:
    cache_dir = fm.get_cachedir()
    if os.path.exists(cache_dir):
        import shutil
        shutil.rmtree(cache_dir)
        print("已清除matplotlib字体缓存")
        # 重建字体缓存
        fm.fontManager.__init__()
except:
    pass

# 使用成功的字体配置（基于之前测试成功的版本）
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

# 设置图形样式
plt.style.use('default')

print(f"字体配置: PingFang SC (苹方)")

# 验证字体设置
def verify_font():
    """验证字体设置是否正确"""
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"当前主字体: {current_font}")

verify_font()

class FinancialAnalyzer:
    def __init__(self, data_folder):
        self.data_folder = data_folder
        self.companies = ['上海环境公司', '远达环保', '首创环保', '龙净环保']
        self.financial_data = {}
        
    def load_data(self):
        """加载所有公司的财务数据"""
        print("正在加载财务数据...")
        
        for company in self.companies:
            self.financial_data[company] = {}
            
            # 加载利润表
            try:
                profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                if company == '首创环保':
                    profit_file = f"{self.data_folder}/{company}利润表.xlsx"
                elif company == '龙净环保':
                    profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                    
                self.financial_data[company]['利润表'] = pd.read_excel(profit_file)
                print(f"已加载 {company} 利润表")
            except Exception as e:
                print(f"加载 {company} 利润表失败: {e}")
                
            # 加载资产负债表
            try:
                balance_file = f"{self.data_folder}/{company}--资产负债表.xlsx"
                if company == '首创环保':
                    balance_file = f"{self.data_folder}/{company}资产负债表.xlsx"
                elif company == '龙净环保':
                    balance_file = f"{self.data_folder}/{company}-资产负债表.xlsx"
                    
                self.financial_data[company]['资产负债表'] = pd.read_excel(balance_file)
                print(f"已加载 {company} 资产负债表")
            except Exception as e:
                print(f"加载 {company} 资产负债表失败: {e}")
                
            # 加载现金流量表
            try:
                cashflow_file = f"{self.data_folder}/{company}--现金流量表.xlsx"
                if company == '首创环保':
                    cashflow_file = f"{self.data_folder}/{company}现金流量表.xlsx"
                elif company == '龙净环保':
                    cashflow_file = f"{self.data_folder}/{company}-现金流量表.xlsx"
                    
                self.financial_data[company]['现金流量表'] = pd.read_excel(cashflow_file)
                print(f"已加载 {company} 现金流量表")
            except Exception as e:
                print(f"加载 {company} 现金流量表失败: {e}")
                
        print("数据加载完成！\n")
        
    def explore_data_structure(self):
        """探索数据结构"""
        print("=== 数据结构探索 ===")
        for company in self.companies:
            print(f"\n{company}:")
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    print(f"  {statement_type}: {df.shape}")
                    print(f"    列名: {list(df.columns)[:5]}...")  # 显示前5列
                    
    def clean_and_prepare_data(self):
        """数据清洗和准备"""
        print("=== 开始数据清洗 ===")
        
        for company in self.companies:
            print(f"\n处理 {company} 的数据...")
            
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    
                    # 检查空值
                    null_count = df.isnull().sum().sum()
                    print(f"  {statement_type} 空值数量: {null_count}")
                    
                    # 填充空值
                    if null_count > 0:
                        # 对数值列使用0填充，对文本列使用'未知'填充
                        numeric_cols = df.select_dtypes(include=[np.number]).columns
                        text_cols = df.select_dtypes(include=['object']).columns
                        
                        df[numeric_cols] = df[numeric_cols].fillna(0)
                        df[text_cols] = df[text_cols].fillna('未知')
                        
                        print(f"    已填充空值")
                    
                    # 更新数据
                    self.financial_data[company][statement_type] = df
                    
        print("数据清洗完成！\n")

    def examine_sample_data(self):
        """查看样本数据"""
        print("=== 样本数据查看 ===")

        # 查看第一家公司的数据结构
        company = self.companies[0]
        print(f"\n{company} 利润表前几行:")
        if '利润表' in self.financial_data[company]:
            df = self.financial_data[company]['利润表']
            print(df.head())
            print(f"\n列名: {list(df.columns)}")

        print(f"\n{company} 资产负债表前几行:")
        if '资产负债表' in self.financial_data[company]:
            df = self.financial_data[company]['资产负债表']
            print(df.head())

    def extract_financial_indicators(self):
        """提取财务指标"""
        print("=== 提取财务指标 ===")

        self.indicators = {}

        for company in self.companies:
            print(f"\n处理 {company}...")
            self.indicators[company] = {}

            # 获取各报表数据
            profit_df = self.financial_data[company].get('利润表')
            balance_df = self.financial_data[company].get('资产负债表')
            cashflow_df = self.financial_data[company].get('现金流量表')

            if profit_df is not None and balance_df is not None:
                # 获取年份列（排除第一列项目名称）
                years = [col for col in profit_df.columns if col != profit_df.columns[0]]

                for year in years:
                    year_str = str(year)[:4] if hasattr(year, 'year') else str(year)

                    if year_str not in self.indicators[company]:
                        self.indicators[company][year_str] = {}

                    # 从利润表提取数据
                    营业收入 = self.get_value_by_keyword(profit_df, ['营业收入', '主营业务收入'], year)
                    净利润 = self.get_value_by_keyword(profit_df, ['净利润', '归属于母公司所有者的净利润'], year)
                    营业成本 = self.get_value_by_keyword(profit_df, ['营业成本', '主营业务成本'], year)

                    # 从资产负债表提取数据
                    总资产 = self.get_value_by_keyword(balance_df, ['资产总计', '总资产'], year)
                    总负债 = self.get_value_by_keyword(balance_df, ['负债合计', '总负债'], year)
                    股东权益 = self.get_value_by_keyword(balance_df, ['所有者权益合计', '股东权益合计', '归属于母公司所有者权益合计'], year)
                    流动资产 = self.get_value_by_keyword(balance_df, ['流动资产合计'], year)
                    流动负债 = self.get_value_by_keyword(balance_df, ['流动负债合计'], year)
                    应收账款 = self.get_value_by_keyword(balance_df, ['应收账款', '应收票据及应收账款'], year)
                    存货 = self.get_value_by_keyword(balance_df, ['存货'], year)

                    # 计算财务指标
                    indicators = self.indicators[company][year_str]

                    # 盈利能力指标
                    indicators['营业收入'] = 营业收入
                    indicators['净利润'] = 净利润
                    indicators['净利润率'] = 净利润 / 营业收入 if 营业收入 != 0 else 0
                    indicators['ROA'] = 净利润 / 总资产 if 总资产 != 0 else 0
                    indicators['ROE'] = 净利润 / 股东权益 if 股东权益 != 0 else 0

                    # 偿债能力指标
                    indicators['总资产'] = 总资产
                    indicators['总负债'] = 总负债
                    indicators['股东权益'] = 股东权益
                    indicators['流动比率'] = 流动资产 / 流动负债 if 流动负债 != 0 else 0
                    速动资产 = 流动资产 - 存货 - 应收账款
                    indicators['速动比率'] = 速动资产 / 流动负债 if 流动负债 != 0 else 0
                    indicators['资产负债率'] = 总负债 / 总资产 if 总资产 != 0 else 0

                    # 营运能力指标（简化计算）
                    indicators['应收账款周转天数'] = 63  # 使用题目给定的行业平均值
                    indicators['应付账款周转天数'] = 90
                    indicators['存货周转天数'] = 40
                    indicators['营运周期'] = 63 + 40 - 90  # 13天

        print("财务指标提取完成！")

    def get_value_by_keyword(self, df, keywords, year_col):
        """根据关键词从数据框中获取值"""
        try:
            for keyword in keywords:
                # 在第一列中查找包含关键词的行
                mask = df.iloc[:, 0].astype(str).str.contains(keyword, na=False)
                if mask.any():
                    row_idx = mask.idxmax()
                    value = df.loc[row_idx, year_col]
                    # 转换为数值
                    if pd.isna(value) or value == 0 or value == '未知' or value == '--':
                        return 0
                    return self.convert_to_number(value)
            return 0
        except:
            return 0

    def convert_to_number(self, value):
        """将中文数值格式转换为数字（万元为单位）"""
        try:
            if isinstance(value, (int, float)):
                return float(value)

            value_str = str(value).strip()
            if value_str in ['--', '未知', '', 'nan']:
                return 0

            # 处理亿、万等单位
            if '亿' in value_str:
                num = float(value_str.replace('亿', ''))
                return num * 10000  # 转换为万元
            elif '万' in value_str:
                num = float(value_str.replace('万', ''))
                return num
            else:
                return float(value_str)
        except:
            return 0

    def calculate_growth_rates(self):
        """计算发展能力指标（增长率）"""
        print("=== 计算发展能力指标 ===")

        for company in self.companies:
            print(f"处理 {company}...")
            years = sorted(self.indicators[company].keys())

            for i in range(1, len(years)):
                current_year = years[i]
                previous_year = years[i-1]

                # 计算资产增长率
                current_assets = self.indicators[company][current_year]['总资产']
                previous_assets = self.indicators[company][previous_year]['总资产']

                if previous_assets != 0:
                    asset_growth = (current_assets - previous_assets) / previous_assets
                    self.indicators[company][current_year]['资产增长率'] = asset_growth
                else:
                    self.indicators[company][current_year]['资产增长率'] = 0

                # 计算营业收入增长率
                current_revenue = self.indicators[company][current_year]['营业收入']
                previous_revenue = self.indicators[company][previous_year]['营业收入']

                if previous_revenue != 0:
                    revenue_growth = (current_revenue - previous_revenue) / previous_revenue
                    self.indicators[company][current_year]['营业收入增长率'] = revenue_growth
                else:
                    self.indicators[company][current_year]['营业收入增长率'] = 0

                # 计算净利润增长率
                current_profit = self.indicators[company][current_year]['净利润']
                previous_profit = self.indicators[company][previous_year]['净利润']

                if previous_profit != 0:
                    profit_growth = (current_profit - previous_profit) / previous_profit
                    self.indicators[company][current_year]['净利润增长率'] = profit_growth
                else:
                    self.indicators[company][current_year]['净利润增长率'] = 0

        print("发展能力指标计算完成！")

    def create_summary_dataframe(self):
        """创建汇总数据框"""
        print("=== 创建汇总数据 ===")

        # 创建各项指标的汇总表
        summary_data = []

        for company in self.companies:
            for year, indicators in self.indicators[company].items():
                row = {
                    '公司': company,
                    '年份': year,
                    **indicators
                }
                summary_data.append(row)

        self.summary_df = pd.DataFrame(summary_data)

        # 保存到Excel
        self.summary_df.to_excel('财务指标汇总表.xlsx', index=False)
        print("汇总数据已保存到 财务指标汇总表.xlsx")

        return self.summary_df

    def create_pivot_tables(self):
        """创建数据透视表分析"""
        print("=== 创建数据透视表分析 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 创建Excel文件，包含多个工作表
        with pd.ExcelWriter('财务分析数据透视表.xlsx', engine='openpyxl') as writer:

            # 1. 原始数据表
            self.summary_df.to_excel(writer, sheet_name='原始数据', index=False)

            # 2. 盈利能力透视表
            profitability_pivot = pd.pivot_table(
                self.summary_df,
                values=['净利润率', 'ROA', 'ROE'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            profitability_pivot.to_excel(writer, sheet_name='盈利能力透视表')

            # 3. 偿债能力透视表
            solvency_pivot = pd.pivot_table(
                self.summary_df,
                values=['流动比率', '速动比率', '资产负债率'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            solvency_pivot.to_excel(writer, sheet_name='偿债能力透视表')

            # 4. 发展能力透视表
            if '资产增长率' in self.summary_df.columns:
                growth_pivot = pd.pivot_table(
                    self.summary_df,
                    values=['资产增长率', '营业收入增长率', '净利润增长率'],
                    index='公司',
                    columns='年份',
                    aggfunc='mean'
                )
                growth_pivot.to_excel(writer, sheet_name='发展能力透视表')

            # 5. 规模对比透视表
            scale_pivot = pd.pivot_table(
                self.summary_df,
                values=['营业收入', '净利润', '总资产'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            scale_pivot.to_excel(writer, sheet_name='规模对比透视表')

            # 6. 综合排名表
            latest_data = self.summary_df.groupby('公司').last()

            # 计算各项能力得分
            ranking_data = pd.DataFrame(index=latest_data.index)
            ranking_data['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25)
            ranking_data['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25)
            ranking_data['营运能力得分'] = 25  # 相同
            ranking_data['发展能力得分'] = 25 if '资产增长率' in latest_data.columns else 20
            ranking_data['综合得分'] = ranking_data.sum(axis=1)
            ranking_data = ranking_data.sort_values('综合得分', ascending=False)

            ranking_data.to_excel(writer, sheet_name='综合排名表')

        print("数据透视表已保存到 财务分析数据透视表.xlsx")

    def create_four_abilities_pivot_tables(self):
        """创建四个能力的专门数据透视表"""
        print("=== 创建四个能力专门数据透视表 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 创建四个能力的专门Excel文件
        with pd.ExcelWriter('四个能力数据透视表.xlsx', engine='openpyxl') as writer:

            # 1. 盈利能力数据透视表
            print("创建盈利能力数据透视表...")
            profitability_data = self.summary_df[['公司', '年份', '营业收入', '净利润', '净利润率', 'ROA', 'ROE']].copy()
            profitability_data['净利润率(%)'] = profitability_data['净利润率'] * 100
            profitability_data['ROA(%)'] = profitability_data['ROA'] * 100
            profitability_data['ROE(%)'] = profitability_data['ROE'] * 100

            # 盈利能力汇总表
            profitability_summary = profitability_data.groupby('公司').agg({
                '营业收入': ['mean', 'std', 'min', 'max'],
                '净利润': ['mean', 'std', 'min', 'max'],
                '净利润率(%)': ['mean', 'std', 'min', 'max'],
                'ROA(%)': ['mean', 'std', 'min', 'max'],
                'ROE(%)': ['mean', 'std', 'min', 'max']
            }).round(2)

            profitability_data.to_excel(writer, sheet_name='盈利能力原始数据', index=False)
            profitability_summary.to_excel(writer, sheet_name='盈利能力统计汇总')

            # 2. 偿债能力数据透视表
            print("创建偿债能力数据透视表...")
            solvency_data = self.summary_df[['公司', '年份', '总资产', '总负债', '股东权益', '流动比率', '速动比率', '资产负债率']].copy()
            solvency_data['资产负债率(%)'] = solvency_data['资产负债率'] * 100

            # 偿债能力汇总表
            solvency_summary = solvency_data.groupby('公司').agg({
                '总资产': ['mean', 'std', 'min', 'max'],
                '总负债': ['mean', 'std', 'min', 'max'],
                '股东权益': ['mean', 'std', 'min', 'max'],
                '流动比率': ['mean', 'std', 'min', 'max'],
                '速动比率': ['mean', 'std', 'min', 'max'],
                '资产负债率(%)': ['mean', 'std', 'min', 'max']
            }).round(2)

            solvency_data.to_excel(writer, sheet_name='偿债能力原始数据', index=False)
            solvency_summary.to_excel(writer, sheet_name='偿债能力统计汇总')

            # 3. 营运能力数据透视表
            print("创建营运能力数据透视表...")
            operational_data = pd.DataFrame({
                '公司': self.companies,
                '应收账款周转天数': [63] * len(self.companies),
                '存货周转天数': [40] * len(self.companies),
                '应付账款周转天数': [90] * len(self.companies),
                '营运周期': [13] * len(self.companies),
                '评价': ['资金周转效率高'] * len(self.companies)
            })

            operational_data.to_excel(writer, sheet_name='营运能力数据', index=False)

            # 4. 发展能力数据透视表
            print("创建发展能力数据透视表...")
            if '资产增长率' in self.summary_df.columns:
                growth_data = self.summary_df[['公司', '年份', '资产增长率', '营业收入增长率', '净利润增长率']].copy()
                growth_data = growth_data.dropna()

                if not growth_data.empty:
                    growth_data['资产增长率(%)'] = growth_data['资产增长率'] * 100
                    growth_data['营业收入增长率(%)'] = growth_data['营业收入增长率'] * 100
                    growth_data['净利润增长率(%)'] = growth_data['净利润增长率'] * 100

                    # 发展能力汇总表
                    growth_summary = growth_data.groupby('公司').agg({
                        '资产增长率(%)': ['mean', 'std', 'min', 'max'],
                        '营业收入增长率(%)': ['mean', 'std', 'min', 'max'],
                        '净利润增长率(%)': ['mean', 'std', 'min', 'max']
                    }).round(2)

                    growth_data.to_excel(writer, sheet_name='发展能力原始数据', index=False)
                    growth_summary.to_excel(writer, sheet_name='发展能力统计汇总')

            # 5. 四个能力综合评分表
            print("创建综合评分表...")
            latest_data = self.summary_df.groupby('公司').last()

            comprehensive_score = pd.DataFrame(index=latest_data.index)
            comprehensive_score['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25).round(1)
            comprehensive_score['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25).round(1)
            comprehensive_score['营运能力得分'] = 25.0  # 相同标准

            if '资产增长率' in latest_data.columns:
                comprehensive_score['发展能力得分'] = ((latest_data['资产增长率'] + 0.1) * 25 / 0.2).clip(0, 25).round(1)
            else:
                comprehensive_score['发展能力得分'] = 20.0

            comprehensive_score['综合得分'] = comprehensive_score.sum(axis=1).round(1)
            comprehensive_score['排名'] = comprehensive_score['综合得分'].rank(ascending=False, method='min').astype(int)

            # 添加评级
            def get_rating(score):
                if score >= 90:
                    return 'A+'
                elif score >= 80:
                    return 'A'
                elif score >= 70:
                    return 'B+'
                elif score >= 60:
                    return 'B'
                else:
                    return 'C'

            comprehensive_score['评级'] = comprehensive_score['综合得分'].apply(get_rating)
            comprehensive_score = comprehensive_score.sort_values('综合得分', ascending=False)

            comprehensive_score.to_excel(writer, sheet_name='四个能力综合评分')

        print("四个能力数据透视表已保存到 四个能力数据透视表.xlsx")

    def create_visualizations(self):
        """创建可视化图表"""
        print("=== 创建可视化图表 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 设置图形参数
        plt.rcParams['figure.figsize'] = (12, 8)

        # 1. 盈利能力分析
        self.plot_profitability_analysis()

        # 2. 偿债能力分析
        self.plot_solvency_analysis()

        # 3. 营运能力分析
        self.plot_operational_analysis()

        # 4. 发展能力分析
        self.plot_growth_analysis()

        # 5. 综合对比分析
        self.plot_comprehensive_comparison()

        print("所有图表已生成完成！")

    def test_chinese_font(self):
        """测试中文字体显示"""
        print("=== 测试中文字体显示 ===")

        fig, ax = plt.subplots(figsize=(8, 6))

        # 测试文本
        test_texts = [
            '中文字体测试',
            '盈利能力分析',
            '偿债能力分析',
            '营运能力分析',
            '发展能力分析'
        ]

        # 绘制测试文本
        for i, text in enumerate(test_texts):
            ax.text(0.5, 0.8 - i*0.15, text,
                   fontsize=14, ha='center', va='center',
                   transform=ax.transAxes)

        ax.set_title('中文字体显示测试', fontsize=16, fontweight='bold')
        ax.text(0.5, 0.1, f'当前字体: {plt.rcParams["font.sans-serif"][0]}',
               fontsize=12, ha='center', va='center',
               transform=ax.transAxes, style='italic')

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        plt.tight_layout()
        plt.savefig('中文字体测试.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("字体测试图片已保存为: 中文字体测试.png")
        print("请检查图片中的中文是否正常显示")

    def plot_profitability_analysis(self):
        """盈利能力分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司盈利能力分析', fontsize=16, fontweight='bold')

        # 净利润率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax1.plot(company_data['年份'], company_data['净利润率'] * 100,
                        marker='o', linewidth=2, label=company)
        ax1.set_title('净利润率趋势(%)')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('净利润率(%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # ROA趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax2.plot(company_data['年份'], company_data['ROA'] * 100,
                        marker='s', linewidth=2, label=company)
        ax2.set_title('ROA趋势(%)')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('ROA(%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # ROE趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax3.plot(company_data['年份'], company_data['ROE'] * 100,
                        marker='^', linewidth=2, label=company)
        ax3.set_title('ROE趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('ROE(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 最新年份盈利能力对比柱状图
        ax4 = axes[1, 1]
        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index
        roe_values = latest_data['ROE'] * 100

        bars = ax4.bar(companies, roe_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax4.set_title('最新年份ROE对比(%)')
        ax4.set_ylabel('ROE(%)')
        ax4.tick_params(axis='x', rotation=45)

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, roe_values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value:.2f}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('盈利能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_solvency_analysis(self):
        """偿债能力分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司偿债能力分析', fontsize=16, fontweight='bold')

        # 流动比率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax1.plot(company_data['年份'], company_data['流动比率'],
                        marker='o', linewidth=2, label=company)
        ax1.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='安全线(1.0)')
        ax1.set_title('流动比率趋势')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('流动比率')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 速动比率趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax2.plot(company_data['年份'], company_data['速动比率'],
                        marker='s', linewidth=2, label=company)
        ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='安全线(1.0)')
        ax2.set_title('速动比率趋势')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('速动比率')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 资产负债率趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax3.plot(company_data['年份'], company_data['资产负债率'] * 100,
                        marker='^', linewidth=2, label=company)
        ax3.axhline(y=60, color='red', linestyle='--', alpha=0.7, label='警戒线(60%)')
        ax3.set_title('资产负债率趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('资产负债率(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 最新年份偿债能力对比饼图
        ax4 = axes[1, 1]
        latest_data = self.summary_df.groupby('公司').last()
        debt_ratios = latest_data['资产负债率'] * 100

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        wedges, texts, autotexts = ax4.pie(debt_ratios, labels=debt_ratios.index,
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax4.set_title('最新年份资产负债率分布')

        plt.tight_layout()
        plt.savefig('偿债能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_operational_analysis(self):
        """营运能力分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司营运能力分析', fontsize=16, fontweight='bold')

        # 营运周期对比柱状图
        ax1 = axes[0, 0]
        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index
        cycle_days = [13] * len(companies)  # 使用题目给定的营运周期

        bars = ax1.bar(companies, cycle_days, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax1.set_title('营运周期对比(天)')
        ax1.set_ylabel('天数')
        ax1.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, cycle_days):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value}天', ha='center', va='bottom')

        # 应收账款周转天数
        ax2 = axes[0, 1]
        receivable_days = [63] * len(companies)
        bars2 = ax2.bar(companies, receivable_days, color='lightblue')
        ax2.set_title('应收账款周转天数')
        ax2.set_ylabel('天数')
        ax2.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars2, receivable_days):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value}天', ha='center', va='bottom')

        # 存货周转天数
        ax3 = axes[1, 0]
        inventory_days = [40] * len(companies)
        bars3 = ax3.bar(companies, inventory_days, color='lightgreen')
        ax3.set_title('存货周转天数')
        ax3.set_ylabel('天数')
        ax3.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars3, inventory_days):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value}天', ha='center', va='bottom')

        # 应付账款周转天数
        ax4 = axes[1, 1]
        payable_days = [90] * len(companies)
        bars4 = ax4.bar(companies, payable_days, color='lightcoral')
        ax4.set_title('应付账款周转天数')
        ax4.set_ylabel('天数')
        ax4.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars4, payable_days):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                    f'{value}天', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('营运能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_growth_analysis(self):
        """发展能力分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司发展能力分析', fontsize=16, fontweight='bold')

        # 资产增长率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '资产增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['资产增长率'])
                if not valid_data.empty:
                    ax1.plot(valid_data['年份'], valid_data['资产增长率'] * 100,
                            marker='o', linewidth=2, label=company)
        ax1.set_title('资产增长率趋势(%)')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('资产增长率(%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 营业收入增长率趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '营业收入增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['营业收入增长率'])
                if not valid_data.empty:
                    ax2.plot(valid_data['年份'], valid_data['营业收入增长率'] * 100,
                            marker='s', linewidth=2, label=company)
        ax2.set_title('营业收入增长率趋势(%)')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('营业收入增长率(%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 净利润增长率趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '净利润增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['净利润增长率'])
                if not valid_data.empty:
                    ax3.plot(valid_data['年份'], valid_data['净利润增长率'] * 100,
                            marker='^', linewidth=2, label=company)
        ax3.set_title('净利润增长率趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('净利润增长率(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 最新年份增长率对比
        ax4 = axes[1, 1]
        latest_growth = self.summary_df.groupby('公司').last()
        if '资产增长率' in latest_growth.columns:
            growth_rates = latest_growth['资产增长率'] * 100
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            bars = ax4.bar(growth_rates.index, growth_rates, color=colors)
            ax4.set_title('最新年份资产增长率对比(%)')
            ax4.set_ylabel('资产增长率(%)')
            ax4.tick_params(axis='x', rotation=45)
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)

            for bar, value in zip(bars, growth_rates):
                ax4.text(bar.get_x() + bar.get_width()/2,
                        bar.get_height() + (0.5 if value >= 0 else -1),
                        f'{value:.1f}%', ha='center',
                        va='bottom' if value >= 0 else 'top')

        plt.tight_layout()
        plt.savefig('发展能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_comprehensive_comparison(self):
        """综合对比分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司综合财务能力对比分析', fontsize=16, fontweight='bold')

        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index

        # 四维雷达图 - 综合能力对比
        ax1 = axes[0, 0]
        categories = ['盈利能力', '偿债能力', '营运能力', '发展能力']

        # 标准化各项指标到0-100分
        profitability_scores = (latest_data['ROE'] * 100).clip(0, 30) / 30 * 100
        solvency_scores = (2 - latest_data['资产负债率']).clip(0, 1) * 100
        operational_scores = [75] * len(companies)  # 基于营运周期的标准化分数
        growth_scores = (latest_data.get('资产增长率', pd.Series([0]*len(companies))) * 100 + 10).clip(0, 20) / 20 * 100

        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合雷达图

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

        for i, company in enumerate(companies):
            values = [profitability_scores.iloc[i], solvency_scores.iloc[i],
                     operational_scores[i], growth_scores.iloc[i]]
            values += values[:1]  # 闭合数据

            ax1.plot(angles, values, 'o-', linewidth=2, label=company, color=colors[i])
            ax1.fill(angles, values, alpha=0.25, color=colors[i])

        ax1.set_xticks(angles[:-1])
        ax1.set_xticklabels(categories)
        ax1.set_ylim(0, 100)
        ax1.set_title('综合财务能力雷达图')
        ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax1.grid(True)

        # 营业收入规模对比
        ax2 = axes[0, 1]
        revenue_data = latest_data['营业收入']
        bars = ax2.bar(companies, revenue_data, color=colors)
        ax2.set_title('最新年份营业收入规模对比(万元)')
        ax2.set_ylabel('营业收入(万元)')
        ax2.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, revenue_data):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(revenue_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        # 净利润规模对比
        ax3 = axes[1, 0]
        profit_data = latest_data['净利润']
        bars = ax3.bar(companies, profit_data, color=colors)
        ax3.set_title('最新年份净利润规模对比(万元)')
        ax3.set_ylabel('净利润(万元)')
        ax3.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, profit_data):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(profit_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        # 总资产规模对比
        ax4 = axes[1, 1]
        asset_data = latest_data['总资产']
        bars = ax4.bar(companies, asset_data, color=colors)
        ax4.set_title('最新年份总资产规模对比(万元)')
        ax4.set_ylabel('总资产(万元)')
        ax4.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, asset_data):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(asset_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig('综合对比分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_analysis_report(self):
        """生成分析报告"""
        print("=== 生成财务分析报告 ===")

        latest_data = self.summary_df.groupby('公司').last()

        report = []
        report.append("# 四家环保公司财务分析报告\n")
        report.append("## 一、数据概况")
        report.append(f"本次分析涵盖{len(self.companies)}家环保公司：{', '.join(self.companies)}")
        report.append(f"分析期间：2020-2024年")
        report.append(f"数据来源：各公司年度财务报表\n")

        report.append("## 二、盈利能力分析")
        report.append("### 2.1 净利润率分析")
        for company in self.companies:
            roe = latest_data.loc[company, 'ROE'] * 100
            roa = latest_data.loc[company, 'ROA'] * 100
            profit_margin = latest_data.loc[company, '净利润率'] * 100

            report.append(f"**{company}**:")
            report.append(f"- 净利润率: {profit_margin:.2f}%")
            report.append(f"- ROA: {roa:.2f}%")
            report.append(f"- ROE: {roe:.2f}%")

            if roe > 15:
                report.append("  评价：盈利能力优秀")
            elif roe > 10:
                report.append("  评价：盈利能力良好")
            else:
                report.append("  评价：盈利能力有待提升")
            report.append("")

        report.append("## 三、偿债能力分析")
        for company in self.companies:
            current_ratio = latest_data.loc[company, '流动比率']
            quick_ratio = latest_data.loc[company, '速动比率']
            debt_ratio = latest_data.loc[company, '资产负债率'] * 100

            report.append(f"**{company}**:")
            report.append(f"- 流动比率: {current_ratio:.2f}")
            report.append(f"- 速动比率: {quick_ratio:.2f}")
            report.append(f"- 资产负债率: {debt_ratio:.2f}%")

            if current_ratio >= 1.5 and debt_ratio < 60:
                report.append("  评价：偿债能力强")
            elif current_ratio >= 1.0 and debt_ratio < 70:
                report.append("  评价：偿债能力一般")
            else:
                report.append("  评价：偿债能力较弱")
            report.append("")

        report.append("## 四、营运能力分析")
        report.append("根据行业标准，各公司营运能力指标如下：")
        report.append("- 应收账款周转天数：63天")
        report.append("- 存货周转天数：40天")
        report.append("- 应付账款周转天数：90天")
        report.append("- 营运周期：13天（63+40-90）")
        report.append("营运周期较短，说明资金周转效率较高。\n")

        report.append("## 五、发展能力分析")
        for company in self.companies:
            if '资产增长率' in latest_data.columns:
                asset_growth = latest_data.loc[company, '资产增长率'] * 100
                report.append(f"**{company}**:")
                report.append(f"- 资产增长率: {asset_growth:.2f}%")

                if asset_growth > 10:
                    report.append("  评价：发展能力强")
                elif asset_growth > 0:
                    report.append("  评价：发展能力一般")
                else:
                    report.append("  评价：发展能力较弱")
                report.append("")

        report.append("## 六、综合评价与建议")
        report.append("### 6.1 综合排名")

        # 计算综合得分
        scores = {}
        for company in self.companies:
            roe_score = min(latest_data.loc[company, 'ROE'] * 100 / 15 * 25, 25)
            solvency_score = min((2 - latest_data.loc[company, '资产负债率']) * 25, 25)
            operational_score = 25  # 营运能力相同
            growth_score = 25 if latest_data.loc[company].get('资产增长率', 0) > 0 else 15

            total_score = roe_score + solvency_score + operational_score + growth_score
            scores[company] = total_score

        sorted_companies = sorted(scores.items(), key=lambda x: x[1], reverse=True)

        for i, (company, score) in enumerate(sorted_companies):
            report.append(f"{i+1}. {company}: {score:.1f}分")

        report.append("\n### 6.2 投资建议")
        best_company = sorted_companies[0][0]
        report.append(f"推荐投资标的：**{best_company}**")
        report.append("理由：综合财务指标表现最佳，具有较强的盈利能力和良好的财务结构。")

        # 保存报告
        with open('财务分析报告.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print("分析报告已保存到 财务分析报告.md")
        return '\n'.join(report)

    def generate_word_report(self):
        """生成Word格式的完整分析报告"""
        print("=== 生成Word分析报告 ===")

        try:
            from docx import Document
            from docx.shared import Inches, Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.table import WD_TABLE_ALIGNMENT
        except ImportError:
            print("需要安装python-docx库: pip install python-docx")
            return

        # 创建Word文档
        doc = Document()

        # 设置文档标题
        title = doc.add_heading('四家环保公司财务分析报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加目录
        doc.add_heading('目录', level=1)
        toc_items = [
            '一、分析概述',
            '二、数据来源与方法',
            '三、盈利能力分析',
            '四、偿债能力分析',
            '五、营运能力分析',
            '六、发展能力分析',
            '七、综合对比分析',
            '八、投资建议与结论'
        ]
        for item in toc_items:
            p = doc.add_paragraph(item)
            p.style = 'List Number'

        doc.add_page_break()

        # 一、分析概述
        doc.add_heading('一、分析概述', level=1)
        overview_text = f"""
        本报告对{len(self.companies)}家环保公司（{', '.join(self.companies)}）进行了全面的财务分析。
        分析期间为2020-2024年，涵盖了盈利能力、偿债能力、营运能力和发展能力四个维度。
        通过定量分析和可视化展示，为投资决策提供科学依据。
        """
        doc.add_paragraph(overview_text.strip())

        # 二、数据来源与方法
        doc.add_heading('二、数据来源与方法', level=1)
        doc.add_heading('2.1 数据来源', level=2)
        doc.add_paragraph('数据来源于各公司年度财务报表，包括利润表、资产负债表和现金流量表。')

        doc.add_heading('2.2 分析方法', level=2)
        methods_text = """
        采用财务比率分析法，计算关键财务指标：
        • 盈利能力：净利润率、ROA、ROE
        • 偿债能力：流动比率、速动比率、资产负债率
        • 营运能力：应收账款周转天数、存货周转天数、营运周期
        • 发展能力：资产增长率、营业收入增长率、净利润增长率
        """
        doc.add_paragraph(methods_text.strip())

        # 三、盈利能力分析
        doc.add_heading('三、盈利能力分析', level=1)

        latest_data = self.summary_df.groupby('公司').last()

        # 添加盈利能力表格
        doc.add_heading('3.1 盈利能力指标汇总', level=2)
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '公司名称'
        hdr_cells[1].text = '净利润率(%)'
        hdr_cells[2].text = 'ROA(%)'
        hdr_cells[3].text = 'ROE(%)'

        # 数据行
        for company in self.companies:
            row_cells = table.add_row().cells
            row_cells[0].text = company
            row_cells[1].text = f"{latest_data.loc[company, '净利润率'] * 100:.2f}"
            row_cells[2].text = f"{latest_data.loc[company, 'ROA'] * 100:.2f}"
            row_cells[3].text = f"{latest_data.loc[company, 'ROE'] * 100:.2f}"

        # 添加图片
        try:
            doc.add_paragraph()
            doc.add_picture('盈利能力分析.png', width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        except:
            doc.add_paragraph('盈利能力分析图表（请查看盈利能力分析.png文件）')

        # 盈利能力分析结论
        doc.add_heading('3.2 盈利能力分析结论', level=2)
        best_roe_company = latest_data['ROE'].idxmax()
        best_roe_value = latest_data.loc[best_roe_company, 'ROE'] * 100

        profitability_conclusion = f"""
        从盈利能力分析来看：
        1. {best_roe_company}的ROE最高，达到{best_roe_value:.2f}%，盈利能力最强
        2. 各公司净利润率差异较大，反映了不同的盈利模式和成本控制能力
        3. ROA和ROE指标显示了资产使用效率和股东回报水平的差异
        """
        doc.add_paragraph(profitability_conclusion.strip())

        # 四、偿债能力分析
        doc.add_heading('四、偿债能力分析', level=1)

        # 偿债能力表格
        doc.add_heading('4.1 偿债能力指标汇总', level=2)
        table2 = doc.add_table(rows=1, cols=4)
        table2.style = 'Table Grid'
        table2.alignment = WD_TABLE_ALIGNMENT.CENTER

        hdr_cells2 = table2.rows[0].cells
        hdr_cells2[0].text = '公司名称'
        hdr_cells2[1].text = '流动比率'
        hdr_cells2[2].text = '速动比率'
        hdr_cells2[3].text = '资产负债率(%)'

        for company in self.companies:
            row_cells = table2.add_row().cells
            row_cells[0].text = company
            row_cells[1].text = f"{latest_data.loc[company, '流动比率']:.2f}"
            row_cells[2].text = f"{latest_data.loc[company, '速动比率']:.2f}"
            row_cells[3].text = f"{latest_data.loc[company, '资产负债率'] * 100:.2f}"

        # 添加偿债能力图片
        try:
            doc.add_paragraph()
            doc.add_picture('偿债能力分析.png', width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        except:
            doc.add_paragraph('偿债能力分析图表（请查看偿债能力分析.png文件）')

        # 五、营运能力分析
        doc.add_heading('五、营运能力分析', level=1)
        operational_text = """
        根据行业标准分析，各公司营运能力指标如下：
        • 应收账款周转天数：63天
        • 存货周转天数：40天
        • 应付账款周转天数：90天
        • 营运周期：13天（63+40-90）

        营运周期较短，说明四家公司资金周转效率都比较高，这是环保行业的特点。
        """
        doc.add_paragraph(operational_text.strip())

        try:
            doc.add_paragraph()
            doc.add_picture('营运能力分析.png', width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        except:
            doc.add_paragraph('营运能力分析图表（请查看营运能力分析.png文件）')

        # 六、发展能力分析
        doc.add_heading('六、发展能力分析', level=1)

        if '资产增长率' in latest_data.columns:
            # 发展能力表格
            doc.add_heading('6.1 发展能力指标汇总', level=2)
            table3 = doc.add_table(rows=1, cols=2)
            table3.style = 'Table Grid'
            table3.alignment = WD_TABLE_ALIGNMENT.CENTER

            hdr_cells3 = table3.rows[0].cells
            hdr_cells3[0].text = '公司名称'
            hdr_cells3[1].text = '资产增长率(%)'

            for company in self.companies:
                row_cells = table3.add_row().cells
                row_cells[0].text = company
                growth_rate = latest_data.loc[company, '资产增长率'] * 100
                row_cells[1].text = f"{growth_rate:.2f}"

        try:
            doc.add_paragraph()
            doc.add_picture('发展能力分析.png', width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        except:
            doc.add_paragraph('发展能力分析图表（请查看发展能力分析.png文件）')

        # 七、综合对比分析
        doc.add_heading('七、综合对比分析', level=1)

        # 综合排名表
        doc.add_heading('7.1 综合评分排名', level=2)
        scores = {}
        for company in self.companies:
            roe_score = min(latest_data.loc[company, 'ROE'] * 100 / 15 * 25, 25)
            solvency_score = min((2 - latest_data.loc[company, '资产负债率']) * 25, 25)
            operational_score = 25
            growth_score = 25 if latest_data.loc[company].get('资产增长率', 0) > 0 else 15
            total_score = roe_score + solvency_score + operational_score + growth_score
            scores[company] = total_score

        sorted_companies = sorted(scores.items(), key=lambda x: x[1], reverse=True)

        ranking_text = "综合评分排名：\n"
        for i, (company, score) in enumerate(sorted_companies):
            ranking_text += f"{i+1}. {company}: {score:.1f}分\n"

        doc.add_paragraph(ranking_text)

        try:
            doc.add_paragraph()
            doc.add_picture('综合对比分析.png', width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        except:
            doc.add_paragraph('综合对比分析图表（请查看综合对比分析.png文件）')

        # 八、投资建议与结论
        doc.add_heading('八、投资建议与结论', level=1)

        best_company = sorted_companies[0][0]
        conclusion_text = f"""
        基于四个维度的综合分析，得出以下结论：

        1. 投资建议：推荐投资{best_company}
           理由：综合财务指标表现最佳，具有较强的盈利能力和良好的财务结构。

        2. 行业特点：
           • 营运周期短，资金周转效率高
           • 各公司偿债能力普遍良好
           • 发展能力存在差异，体现了不同的发展战略

        3. 风险提示：
           • 本分析基于历史财务数据
           • 投资决策需结合宏观经济、行业政策等因素
           • 建议持续关注公司经营状况变化
        """
        doc.add_paragraph(conclusion_text.strip())

        # 保存Word文档
        doc.save('四家环保公司财务分析报告.docx')
        print("Word分析报告已保存到 四家环保公司财务分析报告.docx")

        return doc

# 创建分析器实例
analyzer = FinancialAnalyzer("4390595892600087646")

# 执行完整分析
if __name__ == "__main__":
    # 首先测试中文字体
    print("=== 开始字体测试 ===")
    analyzer.test_chinese_font()

    print("\n=== 开始完整财务分析 ===")
    analyzer.load_data()
    analyzer.explore_data_structure()
    analyzer.clean_and_prepare_data()
    analyzer.examine_sample_data()
    analyzer.extract_financial_indicators()
    analyzer.calculate_growth_rates()
    analyzer.create_summary_dataframe()
    analyzer.create_pivot_tables()
    analyzer.create_four_abilities_pivot_tables()
    analyzer.create_visualizations()
    analyzer.generate_analysis_report()
    analyzer.generate_word_report()

    print("\n=== 分析完成 ===")
    print("生成的文件：")
    print("1. 财务指标汇总表.xlsx - 详细财务数据")
    print("2. 财务分析数据透视表.xlsx - 数据透视表分析")
    print("3. 四个能力数据透视表.xlsx - 四个能力专门透视表")
    print("4. 盈利能力分析.png - 盈利能力图表")
    print("5. 偿债能力分析.png - 偿债能力图表")
    print("6. 营运能力分析.png - 营运能力图表")
    print("7. 发展能力分析.png - 发展能力图表")
    print("8. 综合对比分析.png - 综合对比图表")
    print("9. 财务分析报告.md - 详细分析报告")
    print("10. 四家环保公司财务分析报告.docx - Word格式完整报告")
